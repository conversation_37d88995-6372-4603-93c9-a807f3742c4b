<?php

namespace App\Filament\Resources\AdminResource\Pages\Auth;

use DiogoGPinto\AuthUIEnhancer\Pages\Auth\Concerns\HasCustomLayout;
use Filament\Forms\Form;
use Filament\Pages\Auth\Login as BaseLogin;

class CustomLogin extends BaseLogin
{
    use HasCustomLayout;
        protected static string $view = 'filament.pages.auth.custom-login';

    protected function hasBrand(): bool
    {
        return false;
    }
    

    public function getTitle(): string
    {
        return '';
    }

    public function getHeading(): string
    {
        return '';
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                $this->getEmailFormComponent()
                    ->label('البريد الإلكتروني')
                    ->extraInputAttributes(['dir' => 'rtl']),
                $this->getPasswordFormComponent()
                    ->label('كلمة المرور')
                    ->extraInputAttributes(['dir' => 'rtl']),
                $this->getRememberFormComponent()
                    ->label('تذكرني'),
            ])
            ->columns(1);
    }

}