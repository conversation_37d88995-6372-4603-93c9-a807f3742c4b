<?php

namespace App\Providers\Filament;

use App\Filament\Resources\AdminResource\Pages\Auth\CustomLogin;
use App\Http\Middleware\SetLocale;
use DiogoGPinto\AuthUIEnhancer\AuthUIEnhancerPlugin;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\MenuItem;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Support\Enums\MaxWidth;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->login(CustomLogin::class)
            ->colors([
                'primary' => Color::hex('#1E78EB'),
                'secondary' => Color::hex('#FFD836'),
                'success' => [
                    '500' => '#22c55e',
                ],
            ])
            ->brandLogo(asset('images/logo.png'))
            ->passwordReset()
            ->profile()
            ->brandLogoHeight('10rem')
            ->brandName('ادرس')
            ->maxContentWidth(MaxWidth::Full)
            ->userMenuItems([
                MenuItem::make()
                    ->label(fn(): string => app()->getLocale() === 'ar' ? 'English' : 'العربية')
                    ->url(fn(): string => '/switch-lang/' . (app()->getLocale() === 'ar' ? 'en' : 'ar'))
                    ->icon('heroicon-m-language'),
            ])
            // Database
            ->databaseNotifications()
            ->databaseTransactions()
            // Auth
            ->spa()
            ->authGuard('web')
            ->authPasswordBroker('users')
            ->globalSearchKeyBindings(['command+k', 'ctrl+k'])

            ->font('Inter') // Uses Google Fonts by default
            ->sidebarCollapsibleOnDesktop() // Built-in feature        
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
            ])
            ->plugins([
                \BezhanSalleh\FilamentShield\FilamentShieldPlugin::make(),
                AuthUIEnhancerPlugin::make()

            ])

            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
                SetLocale::class,

            ]);
    }
}
