<?php if (isset($component)) { $__componentOriginalf45da69382bf4ac45a50b496dc82aa9a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf45da69382bf4ac45a50b496dc82aa9a = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.simple','data' => ['class' => 'font-sans !pt-0 !bg-transparent','dir' => 'rtl']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page.simple'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'font-sans !pt-0 !bg-transparent','dir' => 'rtl']); ?>
    <!-- This completely removes header space -->
    <style>
        .fi-logo {
            display: none !important;
        }

        .fi-header {
            display: none !important;
        }

        .fi-sidebar {
            display: none !important;
        }
    </style>

    <!-- Centered container -->
    <div class="min-h-screen flex flex-col items-center justify-center">
        <div class="relative w-full max-w-sm mx-4">
            <!-- White card content -->
            <div class="bg-white rounded-xl shadow-lg overflow-hidden mt-40 relative">
                <!-- Frame background container with relative positioning -->
                <div class="relative">
                    <img src="<?php echo e(asset('images/Frame.png')); ?>"
                        class="w-full h-auto object-cover">

                    <!-- Logo absolutely positioned in center of frame -->
                    <div class="absolute inset-0 flex items-center justify-center -mt-16">
                        <img src="<?php echo e(asset('images/logo.png')); ?>"
                            alt="Logo"
                            class="h-20 w-auto">
                    </div>
                </div>

                <form wire:submit.prevent="authenticate" id="login" class="px-6 pt-2 pb-4 space-y-3">
                    <?php echo e($this->form); ?>


                    <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['type' => 'submit','form' => 'login','wire:loading.attr' => 'disabled','wire:target' => 'authenticate','class' => 'w-full justify-center mt-4 rounded-full bg-blue-600 hover:bg-blue-700 text-white py-2.5']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'submit','form' => 'login','wire:loading.attr' => 'disabled','wire:target' => 'authenticate','class' => 'w-full justify-center mt-4 rounded-full bg-blue-600 hover:bg-blue-700 text-white py-2.5']); ?>
                        تسجيل الدخول
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
                </form>

                <div class="px-6 pb-4 text-center text-xs text-gray-600">
                    جميع الحقوق محفوظة © <?php echo e(date('Y')); ?>

                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf45da69382bf4ac45a50b496dc82aa9a)): ?>
<?php $attributes = $__attributesOriginalf45da69382bf4ac45a50b496dc82aa9a; ?>
<?php unset($__attributesOriginalf45da69382bf4ac45a50b496dc82aa9a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf45da69382bf4ac45a50b496dc82aa9a)): ?>
<?php $component = $__componentOriginalf45da69382bf4ac45a50b496dc82aa9a; ?>
<?php unset($__componentOriginalf45da69382bf4ac45a50b496dc82aa9a); ?>
<?php endif; ?><?php /**PATH C:\xampp\htdocs\work\new-addrus\resources\views/filament/pages/auth/custom-login.blade.php ENDPATH**/ ?>